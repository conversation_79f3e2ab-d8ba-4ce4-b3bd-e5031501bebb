export interface Env {
  BUCKET: R2Bucket;
  BASE_PATH?: string;
  CONVERSION_SERVICE_URL?: string;
  CONVERSION_SERVICE_TOKEN?: string;
}

export interface CompleteBody {
  parts: R2UploadedPart[];
}

// Conversion service types
export interface ConversionRequest {
  fileKey: string;
  outputFormat: string;
  options?: ConversionOptions;
  callbackUrl?: string;
}

export interface ConversionOptions {
  toc?: boolean;
  template?: string;
  pdfEngine?: string;
  standalone?: boolean;
  customArgs?: string[];
}

export interface ConversionResponse {
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  message?: string;
  outputFileKey?: string;
  error?: string;
}

export interface ConversionJobStatus {
  jobId: string;
  status: 'waiting' | 'active' | 'completed' | 'failed';
  progress: number;
  data?: any;
  result?: {
    outputFile: string;
    filename: string;
    size: number;
  };
  error?: string;
}

export interface SupportedFormats {
  input: string[];
  output: string[];
}

export interface ConversionCompleteBody {
  fileKey: string;
  outputFormat: string;
  options?: ConversionOptions;
}
