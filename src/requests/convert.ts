import { BaseRequest } from './base';
import { ConversionCompleteBody, ConversionResponse, ConversionJobStatus, SupportedFormats } from '../types';

export class ConvertRequest extends BaseRequest {
  
  constructor(
    private readonly bucket: R2Bucket,
    key: string,
    private readonly conversionServiceUrl: string,
    private readonly conversionServiceToken?: string
  ) {
    super(key);
  }

  async execute(body: ConversionCompleteBody | null): Promise<Response> {
    if (body === null) {
      return this.error("Missing or incomplete body");
    }

    try {
      // Verify the file exists in R2
      const object = await this.bucket.head(this.key);
      if (!object) {
        return this.error("File not found in storage", 404);
      }

      // Get the file from R2
      const fileObject = await this.bucket.get(this.key);
      if (!fileObject) {
        return this.error("Failed to retrieve file from storage", 500);
      }

      // Create form data for the conversion service
      const formData = new FormData();
      const fileBlob = new Blob([await fileObject.arrayBuffer()], {
        type: object.httpMetadata?.contentType || 'application/octet-stream'
      });
      
      formData.append('file', fileBlob, this.key);
      formData.append('outputFormat', body.outputFormat);
      
      if (body.options) {
        formData.append('options', JSON.stringify(body.options));
      }

      // Call the conversion service
      const conversionResponse = await this.callConversionService('/convert', {
        method: 'POST',
        body: formData
      });

      if (!conversionResponse.ok) {
        const errorText = await conversionResponse.text();
        return this.error(`Conversion service error: ${errorText}`, conversionResponse.status);
      }

      const result: ConversionResponse = await conversionResponse.json();
      
      return this.json({
        jobId: result.jobId,
        status: result.status,
        message: result.message || 'Conversion job started successfully',
        originalFile: this.key,
        outputFormat: body.outputFormat
      });

    } catch (error: any) {
      console.error('Conversion request error:', error);
      return this.error(`Conversion failed: ${error.message}`, 500);
    }
  }

  private async callConversionService(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.conversionServiceUrl}${endpoint}`;
    const headers: HeadersInit = {
      ...options.headers
    };

    if (this.conversionServiceToken) {
      headers['Authorization'] = `Bearer ${this.conversionServiceToken}`;
    }

    return fetch(url, {
      ...options,
      headers
    });
  }
}

export class ConversionStatusRequest extends BaseRequest {
  
  constructor(
    key: string,
    private readonly jobId: string,
    private readonly conversionServiceUrl: string,
    private readonly conversionServiceToken?: string
  ) {
    super(key);
  }

  async execute(): Promise<Response> {
    try {
      const response = await this.callConversionService(`/status/${this.jobId}`, {
        method: 'GET'
      });

      if (!response.ok) {
        if (response.status === 404) {
          return this.error("Conversion job not found", 404);
        }
        const errorText = await response.text();
        return this.error(`Conversion service error: ${errorText}`, response.status);
      }

      const status: ConversionJobStatus = await response.json();
      
      return this.json({
        jobId: status.jobId,
        status: status.status,
        progress: status.progress,
        result: status.result,
        error: status.error
      });

    } catch (error: any) {
      console.error('Status check error:', error);
      return this.error(`Status check failed: ${error.message}`, 500);
    }
  }

  private async callConversionService(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.conversionServiceUrl}${endpoint}`;
    const headers: HeadersInit = {
      ...options.headers
    };

    if (this.conversionServiceToken) {
      headers['Authorization'] = `Bearer ${this.conversionServiceToken}`;
    }

    return fetch(url, {
      ...options,
      headers
    });
  }
}

export class ConversionDownloadRequest extends BaseRequest {
  
  constructor(
    private readonly bucket: R2Bucket,
    key: string,
    private readonly jobId: string,
    private readonly conversionServiceUrl: string,
    private readonly conversionServiceToken?: string
  ) {
    super(key);
  }

  async execute(): Promise<Response> {
    try {
      // First check if the job is completed
      const statusResponse = await this.callConversionService(`/status/${this.jobId}`, {
        method: 'GET'
      });

      if (!statusResponse.ok) {
        return this.error("Conversion job not found", 404);
      }

      const status: ConversionJobStatus = await statusResponse.json();
      
      if (status.status !== 'completed') {
        return this.error(`Conversion not completed. Current status: ${status.status}`, 400);
      }

      // Download the converted file from the conversion service
      const downloadResponse = await this.callConversionService(`/download/${this.jobId}`, {
        method: 'GET'
      });

      if (!downloadResponse.ok) {
        const errorText = await downloadResponse.text();
        return this.error(`Download failed: ${errorText}`, downloadResponse.status);
      }

      // Get the converted file content
      const convertedFileBuffer = await downloadResponse.arrayBuffer();
      
      // Generate a new key for the converted file
      const originalExtension = this.key.split('.').pop() || '';
      const outputFormat = status.result?.filename?.split('.').pop() || 'pdf';
      const convertedKey = this.key.replace(new RegExp(`\\.${originalExtension}$`), `.${outputFormat}`);

      // Store the converted file in R2
      await this.bucket.put(convertedKey, convertedFileBuffer, {
        httpMetadata: {
          contentType: this.getContentType(outputFormat),
          contentDisposition: `attachment; filename="${status.result?.filename || convertedKey}"`
        }
      });

      return this.json({
        originalFile: this.key,
        convertedFile: convertedKey,
        filename: status.result?.filename,
        size: status.result?.size,
        downloadUrl: `/download/${convertedKey}`
      });

    } catch (error: any) {
      console.error('Download error:', error);
      return this.error(`Download failed: ${error.message}`, 500);
    }
  }

  private getContentType(extension: string): string {
    const contentTypes: Record<string, string> = {
      'pdf': 'application/pdf',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'html': 'text/html',
      'md': 'text/markdown',
      'txt': 'text/plain',
      'epub': 'application/epub+zip',
      'odt': 'application/vnd.oasis.opendocument.text',
      'rtf': 'application/rtf'
    };
    
    return contentTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  private async callConversionService(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.conversionServiceUrl}${endpoint}`;
    const headers: HeadersInit = {
      ...options.headers
    };

    if (this.conversionServiceToken) {
      headers['Authorization'] = `Bearer ${this.conversionServiceToken}`;
    }

    return fetch(url, {
      ...options,
      headers
    });
  }
}

export class ConversionFormatsRequest extends BaseRequest {
  
  constructor(
    key: string,
    private readonly conversionServiceUrl: string,
    private readonly conversionServiceToken?: string
  ) {
    super(key);
  }

  async execute(): Promise<Response> {
    try {
      const response = await this.callConversionService('/formats', {
        method: 'GET'
      });

      if (!response.ok) {
        const errorText = await response.text();
        return this.error(`Conversion service error: ${errorText}`, response.status);
      }

      const formats: SupportedFormats = await response.json();
      
      return this.json(formats);

    } catch (error: any) {
      console.error('Formats request error:', error);
      return this.error(`Failed to get supported formats: ${error.message}`, 500);
    }
  }

  private async callConversionService(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.conversionServiceUrl}${endpoint}`;
    const headers: HeadersInit = {
      ...options.headers
    };

    if (this.conversionServiceToken) {
      headers['Authorization'] = `Bearer ${this.conversionServiceToken}`;
    }

    return fetch(url, {
      ...options,
      headers
    });
  }
}
