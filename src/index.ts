import { Env, CompleteBody, ConversionCompleteBody } from './types';
import { CreateRequest } from './requests/create';
import { CompleteRequest } from './requests/complete';
import { UploadPartRequest } from './requests/upload-part';
import { AbortRequest } from './requests/abort';
import { ConvertRequest, ConversionStatusRequest, ConversionDownloadRequest, ConversionFormatsRequest } from './requests/convert';

export default {
  async fetch(request, env): Promise<Response> {
    let pathname = new URL(request.url).pathname;

    // Remove base path if configured
    if (env.BASE_PATH) {
      const basePath = env.BASE_PATH.startsWith('/') ? env.BASE_PATH : `/${env.BASE_PATH}`;
      if (pathname.startsWith(basePath)) {
        pathname = pathname.slice(basePath.length);
      } else {
        return new Response("Not Found", { status: 404 });
      }
    }

    const pathParts = pathname.slice(1).split('/').filter(part => part.length > 0);

    // Conversion service endpoints
    if (pathParts[0] === 'convert') {
      if (!env.CONVERSION_SERVICE_URL) {
        return new Response("Conversion service not configured", { status: 503 });
      }

      if (request.method === "POST" && pathParts.length === 2) {
        // POST /convert/{key} - Start document conversion
        const body = await request.json<ConversionCompleteBody>().catch(() => null);
        return await new ConvertRequest(
          env.BUCKET,
          pathParts[1],
          env.CONVERSION_SERVICE_URL,
          env.CONVERSION_SERVICE_TOKEN
        ).execute(body);
      }

      if (request.method === "GET" && pathParts.length === 3 && pathParts[2] === 'status') {
        // GET /convert/{key}/{jobId}/status - Get conversion status
        const jobId = new URL(request.url).searchParams.get('jobId');
        if (!jobId) {
          return new Response("Missing jobId parameter", { status: 400 });
        }
        return await new ConversionStatusRequest(
          pathParts[1],
          jobId,
          env.CONVERSION_SERVICE_URL,
          env.CONVERSION_SERVICE_TOKEN
        ).execute();
      }

      if (request.method === "GET" && pathParts.length === 3 && pathParts[2] === 'download') {
        // GET /convert/{key}/{jobId}/download - Download converted file
        const jobId = new URL(request.url).searchParams.get('jobId');
        if (!jobId) {
          return new Response("Missing jobId parameter", { status: 400 });
        }
        return await new ConversionDownloadRequest(
          env.BUCKET,
          pathParts[1],
          jobId,
          env.CONVERSION_SERVICE_URL,
          env.CONVERSION_SERVICE_TOKEN
        ).execute();
      }

      if (request.method === "GET" && pathParts.length === 1) {
        // GET /convert - Get supported formats
        return await new ConversionFormatsRequest(
          '',
          env.CONVERSION_SERVICE_URL,
          env.CONVERSION_SERVICE_TOKEN
        ).execute();
      }
    }

    if (request.method === "POST") {
      if (pathParts.length === 1) {
        // POST /{key} - Create multipart upload
        return await new CreateRequest(env.BUCKET, pathParts[0]).execute();
      }
      if (pathParts.length == 2) {
        // POST /{key}/{uploadId} - Complete multipart upload
        const body = await request.json<CompleteBody>().catch(() => null);
        return await new CompleteRequest(env.BUCKET, pathParts[0], pathParts[1], body).execute();
      }
    }
    if (request.method === "PUT") {
      if (pathParts.length == 3) {
        // PUT /{key}/{uploadId}/{partNumber} - Upload a part
        return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1], pathParts[2], request.body).execute();
      }
    }
    if (request.method === "DELETE" && pathParts.length == 2) {
      // DELETE /{key}/{uploadId} - Abort multipart upload
      return await new AbortRequest(env.BUCKET, pathParts[0], pathParts[1]).execute();
    }
    return new Response("Not Found", { status: 404 });
  },
} satisfies ExportedHandler<Env>;