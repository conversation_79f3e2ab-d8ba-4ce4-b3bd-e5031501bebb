#!/bin/bash

# Pandoc conversion script
# Usage: ./convert.sh <input_file> <output_file> [options]

set -e

INPUT_FILE="$1"
OUTPUT_FILE="$2"
shift 2

# Default options
PDF_ENGINE="xelatex"
TOC=""
TEMPLATE=""
STANDALONE=""

# Parse additional options
while [[ $# -gt 0 ]]; do
  case $1 in
    --toc)
      TOC="--toc"
      shift
      ;;
    --template=*)
      TEMPLATE="--template=${1#*=}"
      shift
      ;;
    --pdf-engine=*)
      PDF_ENGINE="${1#*=}"
      shift
      ;;
    --standalone)
      STANDALONE="--standalone"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Validate input file exists
if [[ ! -f "$INPUT_FILE" ]]; then
  echo "Error: Input file '$INPUT_FILE' does not exist"
  exit 1
fi

# Get file extensions
INPUT_EXT="${INPUT_FILE##*.}"
OUTPUT_EXT="${OUTPUT_FILE##*.}"

# Build pandoc command
PANDOC_CMD="pandoc \"$INPUT_FILE\" -o \"$OUTPUT_FILE\""

# Add format-specific options
case "$OUTPUT_EXT" in
  pdf)
    PANDOC_CMD="$PANDOC_CMD --pdf-engine=$PDF_ENGINE"
    ;;
  html)
    PANDOC_CMD="$PANDOC_CMD $STANDALONE --self-contained"
    ;;
  epub)
    PANDOC_CMD="$PANDOC_CMD --epub-cover-image=/dev/null"
    ;;
esac

# Add optional parameters
if [[ -n "$TOC" ]]; then
  PANDOC_CMD="$PANDOC_CMD $TOC"
fi

if [[ -n "$TEMPLATE" ]]; then
  PANDOC_CMD="$PANDOC_CMD $TEMPLATE"
fi

# Execute conversion
echo "Converting $INPUT_FILE to $OUTPUT_FILE..."
echo "Command: $PANDOC_CMD"

eval "$PANDOC_CMD"

# Verify output file was created
if [[ ! -f "$OUTPUT_FILE" ]]; then
  echo "Error: Conversion failed - output file not created"
  exit 1
fi

echo "Conversion completed successfully"
echo "Output file: $OUTPUT_FILE"
echo "File size: $(du -h "$OUTPUT_FILE" | cut -f1)"
