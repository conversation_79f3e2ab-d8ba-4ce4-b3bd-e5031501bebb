#!/usr/bin/env node

/**
 * Test script for URL-based document conversion
 * Usage: node test-url-conversion.js
 */

const http = require('http');

const testUrl = 'http://localhost:3000';

// Test data
const testCases = [
  {
    name: 'Convert HTML to PDF',
    data: {
      url: 'https://example.com',
      outputFormat: 'pdf',
      options: {}
    }
  },
  {
    name: 'Convert Markdown to HTML',
    data: {
      url: 'https://raw.githubusercontent.com/microsoft/vscode/main/README.md',
      outputFormat: 'html',
      options: { toc: true }
    }
  }
];

async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  try {
    const response = await makeRequest('/health');
    if (response.status === 200) {
      console.log('✅ Health check passed');
      console.log('   Status:', response.data.status);
      console.log('   Timestamp:', response.data.timestamp);
    } else {
      console.log('❌ Health check failed:', response.status);
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
  }
  console.log('');
}

async function testFormats() {
  console.log('🔍 Testing supported formats...');
  try {
    const response = await makeRequest('/formats');
    if (response.status === 200) {
      console.log('✅ Formats endpoint working');
      console.log('   Input formats:', response.data.input.join(', '));
      console.log('   Output formats:', response.data.output.join(', '));
    } else {
      console.log('❌ Formats endpoint failed:', response.status);
    }
  } catch (error) {
    console.log('❌ Formats endpoint error:', error.message);
  }
  console.log('');
}

async function testConversion(testCase) {
  console.log(`🔍 Testing: ${testCase.name}...`);
  try {
    const response = await makeRequest('/convert', 'POST', testCase.data);
    if (response.status === 200) {
      console.log('✅ Conversion successful');
      console.log('   Job ID:', response.data.jobId);
      console.log('   Status:', response.data.status);
      console.log('   Message:', response.data.message);
      console.log('   Source URL:', response.data.sourceUrl);
      if (response.data.result) {
        console.log('   Output file:', response.data.result.filename);
        console.log('   File size:', response.data.result.size, 'bytes');
      }
    } else {
      console.log('❌ Conversion failed:', response.status);
      console.log('   Error:', response.data.error || response.data);
    }
  } catch (error) {
    console.log('❌ Conversion error:', error.message);
  }
  console.log('');
}

async function testInvalidRequests() {
  console.log('🔍 Testing invalid requests...');
  
  // Test missing URL
  try {
    const response = await makeRequest('/convert', 'POST', { outputFormat: 'pdf' });
    if (response.status === 400 && response.data.error.includes('No URL provided')) {
      console.log('✅ Missing URL validation working');
    } else {
      console.log('❌ Missing URL validation failed');
    }
  } catch (error) {
    console.log('❌ Missing URL test error:', error.message);
  }

  // Test invalid URL
  try {
    const response = await makeRequest('/convert', 'POST', { 
      url: 'not-a-valid-url',
      outputFormat: 'pdf' 
    });
    if (response.status === 400 && response.data.error.includes('Invalid URL')) {
      console.log('✅ Invalid URL validation working');
    } else {
      console.log('❌ Invalid URL validation failed');
    }
  } catch (error) {
    console.log('❌ Invalid URL test error:', error.message);
  }

  // Test unsupported protocol
  try {
    const response = await makeRequest('/convert', 'POST', { 
      url: 'ftp://example.com/file.txt',
      outputFormat: 'pdf' 
    });
    if (response.status === 400 && response.data.error.includes('Only HTTP and HTTPS')) {
      console.log('✅ Protocol validation working');
    } else {
      console.log('❌ Protocol validation failed');
    }
  } catch (error) {
    console.log('❌ Protocol test error:', error.message);
  }

  console.log('');
}

async function runTests() {
  console.log('🚀 Starting URL-based conversion service tests...\n');
  
  await testHealthCheck();
  await testFormats();
  await testInvalidRequests();
  
  for (const testCase of testCases) {
    await testConversion(testCase);
  }
  
  console.log('✨ Tests completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, makeRequest };
