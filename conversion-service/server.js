const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');
const https = require('https');
const http = require('http');
const { URL } = require('url');



const app = express();
const PORT = process.env.PORT || 3000;

// Configuration
const config = {
  maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
  conversionTimeout: parseInt(process.env.CONVERSION_TIMEOUT) || 300000, // 5 minutes
  tempDir: process.env.TEMP_DIR || '/app/temp',
  inputDir: process.env.INPUT_DIR || '/app/input',
  outputDir: process.env.OUTPUT_DIR || '/app/output'
};

// Ensure directories exist
fs.ensureDirSync(config.tempDir);
fs.ensureDirSync(config.inputDir);
fs.ensureDirSync(config.outputDir);



// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many conversion requests, please try again later.'
});
app.use('/convert', limiter);

// Supported MIME types for input files
const allowedMimes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/markdown',
  'text/html',
  'application/rtf',
  'application/vnd.oasis.opendocument.text'
];

// Supported conversion formats
const supportedFormats = {
  input: ['docx', 'doc', 'pdf', 'txt', 'md', 'html', 'rtf', 'odt'],
  output: ['pdf', 'docx', 'html', 'md', 'txt', 'epub', 'odt', 'rtf']
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

// Get supported formats
app.get('/formats', (req, res) => {
  res.json(supportedFormats);
});

// Convert document endpoint
app.post('/convert', async (req, res) => {
  try {
    const { url, outputFormat = 'pdf', options = {} } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'No URL provided' });
    }

    if (!supportedFormats.output.includes(outputFormat)) {
      return res.status(400).json({
        error: 'Unsupported output format',
        supportedFormats: supportedFormats.output
      });
    }

    // Validate URL
    let parsedUrl;
    try {
      parsedUrl = new URL(url);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid URL provided' });
    }

    // Only allow HTTP and HTTPS protocols
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      return res.status(400).json({ error: 'Only HTTP and HTTPS URLs are supported' });
    }

    const jobId = uuidv4();

    // Download file from URL
    const { inputFile, originalName } = await downloadFileFromUrl(url, jobId);

    // Process conversion directly
    const result = await processConversion({
      jobId,
      inputFile,
      originalName,
      outputFormat,
      options
    });

    res.json({
      jobId,
      status: 'completed',
      message: 'Conversion completed successfully',
      result,
      sourceUrl: url
    });

  } catch (error) {
    console.error('Conversion error:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

// Process conversion function
async function processConversion({ jobId, inputFile, originalName, outputFormat, options }) {
  try {
    const inputExt = path.extname(originalName).toLowerCase().slice(1);
    const outputFileName = `${path.basename(originalName, path.extname(originalName))}.${outputFormat}`;
    const outputFile = path.join(config.outputDir, `${jobId}-${outputFileName}`);

    // Build pandoc command
    const pandocCmd = buildPandocCommand(inputFile, outputFile, inputExt, outputFormat, options);

    // Execute conversion
    await execPromise(pandocCmd, config.conversionTimeout);

    // Verify output file exists
    if (!fs.existsSync(outputFile)) {
      throw new Error('Conversion failed - output file not created');
    }

    // Clean up input file
    fs.removeSync(inputFile);

    return {
      outputFile,
      filename: outputFileName,
      size: fs.statSync(outputFile).size
    };

  } catch (error) {
    // Clean up files on error
    if (fs.existsSync(inputFile)) {
      fs.removeSync(inputFile);
    }
    throw error;
  }
}

// Utility functions
function parseFileSize(size) {
  const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
  const match = size.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) return 10 * 1024 * 1024; // Default 10MB
  return parseFloat(match[1]) * units[match[2].toUpperCase()];
}

async function downloadFileFromUrl(url, jobId) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const protocol = parsedUrl.protocol === 'https:' ? https : http;

    // Extract filename from URL or generate one
    let originalName = path.basename(parsedUrl.pathname) || 'document';
    if (!path.extname(originalName)) {
      originalName += '.html'; // Default extension
    }

    const inputFile = path.join(config.inputDir, `${jobId}-${originalName}`);
    const file = fs.createWriteStream(inputFile);

    const request = protocol.get(url, (response) => {
      // Check for redirect
      if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
        file.close();
        fs.removeSync(inputFile);
        return downloadFileFromUrl(response.headers.location, jobId)
          .then(resolve)
          .catch(reject);
      }

      if (response.statusCode !== 200) {
        file.close();
        fs.removeSync(inputFile);
        return reject(new Error(`Failed to download file: HTTP ${response.statusCode}`));
      }

      // Check content type if available
      const contentType = response.headers['content-type'];
      if (contentType && !allowedMimes.some(mime => contentType.includes(mime.split('/')[1]))) {
        // Allow text content types and common document types
        const allowedContentTypes = ['text/', 'application/pdf', 'application/msword', 'application/vnd.'];
        if (!allowedContentTypes.some(type => contentType.includes(type))) {
          file.close();
          fs.removeSync(inputFile);
          return reject(new Error(`Unsupported content type: ${contentType}`));
        }
      }

      // Check file size
      const contentLength = parseInt(response.headers['content-length']);
      if (contentLength && contentLength > parseFileSize(config.maxFileSize)) {
        file.close();
        fs.removeSync(inputFile);
        return reject(new Error(`File too large: ${contentLength} bytes`));
      }

      let downloadedBytes = 0;
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length;
        if (downloadedBytes > parseFileSize(config.maxFileSize)) {
          file.close();
          fs.removeSync(inputFile);
          return reject(new Error(`File too large: exceeded ${config.maxFileSize}`));
        }
      });

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve({ inputFile, originalName });
      });

      file.on('error', (err) => {
        file.close();
        fs.removeSync(inputFile);
        reject(err);
      });
    });

    request.on('error', (err) => {
      if (fs.existsSync(inputFile)) {
        fs.removeSync(inputFile);
      }
      reject(new Error(`Failed to download file: ${err.message}`));
    });

    // Set timeout
    request.setTimeout(30000, () => {
      request.destroy();
      if (fs.existsSync(inputFile)) {
        fs.removeSync(inputFile);
      }
      reject(new Error('Download timeout'));
    });
  });
}

function buildPandocCommand(inputFile, outputFile, inputFormat, outputFormat, options) {
  let cmd = `pandoc "${inputFile}" -o "${outputFile}"`;
  
  // Add format specifications if needed
  if (inputFormat && inputFormat !== 'auto') {
    cmd += ` -f ${inputFormat}`;
  }
  
  // Add output format specific options
  switch (outputFormat) {
    case 'pdf':
      cmd += ' --pdf-engine=xelatex';
      break;
    case 'html':
      cmd += ' --standalone --self-contained';
      break;
    case 'epub':
      cmd += ' --epub-cover-image=/dev/null';
      break;
  }
  
  // Add custom options
  if (options.toc) {
    cmd += ' --toc';
  }
  
  if (options.template) {
    cmd += ` --template="${options.template}"`;
  }
  
  return cmd;
}

function execPromise(command, timeout) {
  return new Promise((resolve, reject) => {
    const child = exec(command, { timeout }, (error, stdout, stderr) => {
      if (error) {
        reject(new Error(`Command failed: ${error.message}\nStderr: ${stderr}`));
      } else {
        resolve(stdout);
      }
    });
  });
}

// Error handling
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Pandoc conversion service running on port ${PORT}`);
  console.log(`Supported input formats: ${supportedFormats.input.join(', ')}`);
  console.log(`Supported output formats: ${supportedFormats.output.join(', ')}`);
});

module.exports = app;
