const express = require('express');
const multer = require('multer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');
const mime = require('mime-types');


const app = express();
const PORT = process.env.PORT || 3000;

// Configuration
const config = {
  maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
  conversionTimeout: parseInt(process.env.CONVERSION_TIMEOUT) || 300000, // 5 minutes
  tempDir: process.env.TEMP_DIR || '/app/temp',
  inputDir: process.env.INPUT_DIR || '/app/input',
  outputDir: process.env.OUTPUT_DIR || '/app/output',
  redisUrl: process.env.REDIS_URL || 'redis://redis:6379'
};

// Ensure directories exist
fs.ensureDirSync(config.tempDir);
fs.ensureDirSync(config.inputDir);
fs.ensureDirSync(config.outputDir);



// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many conversion requests, please try again later.'
});
app.use('/convert', limiter);

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, config.inputDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseFileSize(config.maxFileSize)
  },
  fileFilter: (req, file, cb) => {
    // Allow common document formats
    const allowedMimes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/rtf',
      'application/vnd.oasis.opendocument.text'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'), false);
    }
  }
});

// Supported conversion formats
const supportedFormats = {
  input: ['docx', 'doc', 'pdf', 'txt', 'md', 'html', 'rtf', 'odt'],
  output: ['pdf', 'docx', 'html', 'md', 'txt', 'epub', 'odt', 'rtf']
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

// Get supported formats
app.get('/formats', (req, res) => {
  res.json(supportedFormats);
});

// Convert document endpoint
app.post('/convert', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { outputFormat = 'pdf', options = {} } = req.body;

    if (!supportedFormats.output.includes(outputFormat)) {
      return res.status(400).json({
        error: 'Unsupported output format',
        supportedFormats: supportedFormats.output
      });
    }

    const jobId = uuidv4();
    const inputFile = req.file.path;
    const originalName = req.file.originalname;

    // Process conversion directly
    const result = await processConversion({
      jobId,
      inputFile,
      originalName,
      outputFormat,
      options
    });

    res.json({
      jobId,
      status: 'completed',
      message: 'Conversion completed successfully',
      result
    });

  } catch (error) {
    console.error('Conversion error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Process conversion function
async function processConversion({ jobId, inputFile, originalName, outputFormat, options }) {
  try {
    const inputExt = path.extname(originalName).toLowerCase().slice(1);
    const outputFileName = `${path.basename(originalName, path.extname(originalName))}.${outputFormat}`;
    const outputFile = path.join(config.outputDir, `${jobId}-${outputFileName}`);

    // Build pandoc command
    const pandocCmd = buildPandocCommand(inputFile, outputFile, inputExt, outputFormat, options);

    // Execute conversion
    await execPromise(pandocCmd, config.conversionTimeout);

    // Verify output file exists
    if (!fs.existsSync(outputFile)) {
      throw new Error('Conversion failed - output file not created');
    }

    // Clean up input file
    fs.removeSync(inputFile);

    return {
      outputFile,
      filename: outputFileName,
      size: fs.statSync(outputFile).size
    };

  } catch (error) {
    // Clean up files on error
    if (fs.existsSync(inputFile)) {
      fs.removeSync(inputFile);
    }
    throw error;
  }
}

// Utility functions
function parseFileSize(size) {
  const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
  const match = size.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) return 10 * 1024 * 1024; // Default 10MB
  return parseFloat(match[1]) * units[match[2].toUpperCase()];
}

function buildPandocCommand(inputFile, outputFile, inputFormat, outputFormat, options) {
  let cmd = `pandoc "${inputFile}" -o "${outputFile}"`;
  
  // Add format specifications if needed
  if (inputFormat && inputFormat !== 'auto') {
    cmd += ` -f ${inputFormat}`;
  }
  
  // Add output format specific options
  switch (outputFormat) {
    case 'pdf':
      cmd += ' --pdf-engine=xelatex';
      break;
    case 'html':
      cmd += ' --standalone --self-contained';
      break;
    case 'epub':
      cmd += ' --epub-cover-image=/dev/null';
      break;
  }
  
  // Add custom options
  if (options.toc) {
    cmd += ' --toc';
  }
  
  if (options.template) {
    cmd += ` --template="${options.template}"`;
  }
  
  return cmd;
}

function execPromise(command, timeout) {
  return new Promise((resolve, reject) => {
    const child = exec(command, { timeout }, (error, stdout, stderr) => {
      if (error) {
        reject(new Error(`Command failed: ${error.message}\nStderr: ${stderr}`));
      } else {
        resolve(stdout);
      }
    });
  });
}

// Error handling
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Pandoc conversion service running on port ${PORT}`);
  console.log(`Supported input formats: ${supportedFormats.input.join(', ')}`);
  console.log(`Supported output formats: ${supportedFormats.output.join(', ')}`);
});

module.exports = app;
