{"name": "pandoc-conversion-service", "version": "1.0.0", "description": "Pandoc document conversion service", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1", "fs-extra": "^11.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["pandoc", "document", "conversion", "docker", "api"], "author": "API Upload Service", "license": "MIT"}