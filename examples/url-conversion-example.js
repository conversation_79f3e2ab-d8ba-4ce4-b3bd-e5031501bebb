#!/usr/bin/env node

/**
 * Example: URL-based Document Conversion
 * 
 * This example demonstrates how to convert documents directly from URLs
 * without needing to upload files first.
 */

const http = require('http');

// Configuration
const CONVERSION_SERVICE_URL = 'http://localhost:3000';

/**
 * Make HTTP request to conversion service
 */
async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, CONVERSION_SERVICE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Convert a document from URL
 */
async function convertFromUrl(url, outputFormat = 'pdf', options = {}) {
  console.log(`🔄 Converting ${url} to ${outputFormat}...`);
  
  try {
    const response = await makeRequest('/convert', 'POST', {
      url,
      outputFormat,
      options
    });

    if (response.status === 200) {
      console.log('✅ Conversion successful!');
      console.log('📄 Job ID:', response.data.jobId);
      console.log('📁 Output file:', response.data.result.filename);
      console.log('📊 File size:', response.data.result.size, 'bytes');
      console.log('🔗 Source URL:', response.data.sourceUrl);
      return response.data;
    } else {
      console.log('❌ Conversion failed:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
    return null;
  }
}

/**
 * Get supported formats
 */
async function getSupportedFormats() {
  try {
    const response = await makeRequest('/formats');
    if (response.status === 200) {
      return response.data;
    }
  } catch (error) {
    console.log('❌ Error getting formats:', error.message);
  }
  return null;
}

/**
 * Check service health
 */
async function checkHealth() {
  try {
    const response = await makeRequest('/health');
    if (response.status === 200) {
      console.log('✅ Service is healthy');
      console.log('⏰ Timestamp:', response.data.timestamp);
      return true;
    }
  } catch (error) {
    console.log('❌ Service health check failed:', error.message);
  }
  return false;
}

/**
 * Main example function
 */
async function runExample() {
  console.log('🚀 URL-based Document Conversion Example\n');

  // Check if service is running
  console.log('1. Checking service health...');
  const isHealthy = await checkHealth();
  if (!isHealthy) {
    console.log('❌ Service is not running. Please start it with: docker-compose up -d');
    return;
  }
  console.log('');

  // Get supported formats
  console.log('2. Getting supported formats...');
  const formats = await getSupportedFormats();
  if (formats) {
    console.log('📥 Input formats:', formats.input.join(', '));
    console.log('📤 Output formats:', formats.output.join(', '));
  }
  console.log('');

  // Example conversions
  const examples = [
    {
      name: 'Convert HTML page to PDF',
      url: 'https://example.com',
      outputFormat: 'pdf',
      options: {}
    },
    {
      name: 'Convert Markdown to HTML with TOC',
      url: 'https://raw.githubusercontent.com/microsoft/vscode/main/README.md',
      outputFormat: 'html',
      options: { toc: true }
    },
    {
      name: 'Convert plain text to PDF',
      url: 'https://www.gutenberg.org/files/74/74-0.txt',
      outputFormat: 'pdf',
      options: {}
    }
  ];

  console.log('3. Running conversion examples...\n');
  
  for (let i = 0; i < examples.length; i++) {
    const example = examples[i];
    console.log(`Example ${i + 1}: ${example.name}`);
    
    const result = await convertFromUrl(
      example.url,
      example.outputFormat,
      example.options
    );
    
    if (result) {
      console.log('✨ Success! File converted and saved.\n');
    } else {
      console.log('💥 Conversion failed.\n');
    }
    
    // Add delay between requests
    if (i < examples.length - 1) {
      console.log('⏳ Waiting 2 seconds before next conversion...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log('🎉 Example completed!');
  console.log('\n💡 Tips:');
  console.log('   - Check the /app/output directory in the Docker container for converted files');
  console.log('   - Use docker-compose logs pandoc-converter to see conversion logs');
  console.log('   - Modify this script to test your own URLs and formats');
}

// Run example if this script is executed directly
if (require.main === module) {
  runExample().catch(console.error);
}

module.exports = {
  convertFromUrl,
  getSupportedFormats,
  checkHealth,
  makeRequest
};
