#!/usr/bin/env python3
"""
Python client for document conversion API
Demonstrates how to upload and convert documents using the API
"""

import requests
import time
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path


class ConversionClient:
    """Client for interacting with the document conversion API"""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})
    
    def upload_file(self, file_path: str, key: Optional[str] = None) -> Dict[str, Any]:
        """Upload a file using multipart upload"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_key = key or file_path.name
        
        try:
            # Step 1: Create multipart upload
            create_response = self.session.post(f"{self.base_url}/{file_key}")
            create_response.raise_for_status()
            upload_data = create_response.json()
            upload_id = upload_data['uploadId']
            
            # Step 2: Upload file in chunks (simplified - single part for demo)
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            upload_response = self.session.put(
                f"{self.base_url}/{file_key}/{upload_id}/1",
                data=file_content
            )
            upload_response.raise_for_status()
            part_data = upload_response.json()
            etag = part_data['etag']
            
            # Step 3: Complete multipart upload
            complete_response = self.session.post(
                f"{self.base_url}/{file_key}/{upload_id}",
                json={'parts': [{'partNumber': 1, 'etag': etag}]}
            )
            complete_response.raise_for_status()
            
            return {'fileKey': file_key, 'uploadId': upload_id}
            
        except requests.RequestException as e:
            raise Exception(f"Upload failed: {e}")
    
    def convert_document(self, file_key: str, output_format: str, 
                        options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Start document conversion"""
        payload = {
            'fileKey': file_key,
            'outputFormat': output_format,
            'options': options or {}
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/convert/{file_key}",
                json=payload
            )
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"Conversion failed: {e}")
    
    def get_conversion_status(self, file_key: str, job_id: str) -> Dict[str, Any]:
        """Check conversion status"""
        try:
            response = self.session.get(
                f"{self.base_url}/convert/{file_key}/status",
                params={'jobId': job_id}
            )
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"Status check failed: {e}")
    
    def download_converted_file(self, file_key: str, job_id: str) -> Dict[str, Any]:
        """Download converted file"""
        try:
            response = self.session.get(
                f"{self.base_url}/convert/{file_key}/download",
                params={'jobId': job_id}
            )
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"Download failed: {e}")
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get supported input and output formats"""
        try:
            response = self.session.get(f"{self.base_url}/convert")
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"Failed to get formats: {e}")
    
    def wait_for_conversion(self, file_key: str, job_id: str, 
                           max_wait_time: int = 300, poll_interval: int = 5) -> Dict[str, Any]:
        """Poll conversion status until completion"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status = self.get_conversion_status(file_key, job_id)
            
            if status['status'] == 'completed':
                return status
            
            if status['status'] == 'failed':
                error_msg = status.get('error', 'Unknown error')
                raise Exception(f"Conversion failed: {error_msg}")
            
            print(f"Conversion status: {status['status']} ({status.get('progress', 0)}%)")
            time.sleep(poll_interval)
        
        raise Exception("Conversion timeout")
    
    def upload_and_convert(self, file_path: str, output_format: str,
                          options: Optional[Dict[str, Any]] = None,
                          key: Optional[str] = None) -> Dict[str, Any]:
        """Complete workflow: upload and convert"""
        try:
            # Upload file
            print("Uploading file...")
            upload_result = self.upload_file(file_path, key)
            file_key = upload_result['fileKey']
            
            # Start conversion
            print("Starting conversion...")
            conversion_result = self.convert_document(file_key, output_format, options)
            job_id = conversion_result['jobId']
            
            # Wait for completion
            print("Waiting for conversion to complete...")
            final_status = self.wait_for_conversion(file_key, job_id)
            
            # Get download info
            print("Getting download information...")
            download_result = self.download_converted_file(file_key, job_id)
            
            return {
                'originalFile': file_key,
                'convertedFile': download_result['convertedFile'],
                'downloadUrl': download_result['downloadUrl'],
                'status': final_status
            }
            
        except Exception as e:
            print(f"Upload and convert workflow failed: {e}")
            raise


def main():
    """Example usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert documents using the API')
    parser.add_argument('file', help='Path to the file to convert')
    parser.add_argument('--output-format', '-f', required=True,
                       help='Output format (pdf, docx, html, md, txt, epub, odt, rtf)')
    parser.add_argument('--base-url', '-u', default='https://your-domain.com/api/upload',
                       help='API base URL')
    parser.add_argument('--api-key', '-k', help='API key for authentication')
    parser.add_argument('--toc', action='store_true', help='Generate table of contents')
    parser.add_argument('--standalone', action='store_true', help='Create standalone document')
    parser.add_argument('--key', help='Custom file key (defaults to filename)')
    
    args = parser.parse_args()
    
    # Create client
    client = ConversionClient(args.base_url, args.api_key)
    
    try:
        # Get supported formats
        print("Getting supported formats...")
        formats = client.get_supported_formats()
        print(f"Supported input formats: {', '.join(formats['input'])}")
        print(f"Supported output formats: {', '.join(formats['output'])}")
        
        # Validate output format
        if args.output_format not in formats['output']:
            print(f"Error: Unsupported output format '{args.output_format}'")
            return 1
        
        # Prepare conversion options
        options = {}
        if args.toc:
            options['toc'] = True
        if args.standalone:
            options['standalone'] = True
        
        # Convert document
        result = client.upload_and_convert(
            args.file,
            args.output_format,
            options,
            args.key
        )
        
        print("\nConversion completed successfully!")
        print(f"Original file: {result['originalFile']}")
        print(f"Converted file: {result['convertedFile']}")
        print(f"Download URL: {result['downloadUrl']}")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
