<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Conversion Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .progress {
            margin: 20px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        
        .download-link:hover {
            background-color: #218838;
        }
        
        .formats-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .formats-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }
        
        .format-tag {
            background-color: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Document Conversion Service</h1>
        
        <form id="conversionForm">
            <div class="form-group">
                <label for="fileInput">Select Document:</label>
                <input type="file" id="fileInput" accept=".docx,.doc,.pdf,.txt,.md,.html,.rtf,.odt" required>
            </div>
            
            <div class="form-group">
                <label for="outputFormat">Output Format:</label>
                <select id="outputFormat" required>
                    <option value="">Select format...</option>
                    <option value="pdf">PDF</option>
                    <option value="docx">Word Document (.docx)</option>
                    <option value="html">HTML</option>
                    <option value="md">Markdown</option>
                    <option value="txt">Plain Text</option>
                    <option value="epub">EPUB</option>
                    <option value="odt">OpenDocument Text</option>
                    <option value="rtf">Rich Text Format</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="tocOption"> Generate Table of Contents
                </label>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="standaloneOption"> Create Standalone Document
                </label>
            </div>
            
            <button type="submit" id="convertButton">Convert Document</button>
        </form>
        
        <div class="progress" id="progressContainer">
            <div>Converting document...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">0%</div>
        </div>
        
        <div class="status" id="statusContainer">
            <div id="statusText"></div>
            <div id="downloadContainer"></div>
        </div>
        
        <div class="formats-info">
            <h3>Supported Input Formats:</h3>
            <div class="formats-list" id="inputFormats">
                <span class="format-tag">Loading...</span>
            </div>
            
            <h3>Supported Output Formats:</h3>
            <div class="formats-list" id="outputFormats">
                <span class="format-tag">Loading...</span>
            </div>
        </div>
    </div>

    <script src="conversion-client.js"></script>
    <script>
        // Initialize the conversion client
        const client = new ConversionClient('https://your-domain.com/api/upload');
        
        // Load supported formats on page load
        async function loadSupportedFormats() {
            try {
                const formats = await client.getSupportedFormats();
                
                const inputFormatsContainer = document.getElementById('inputFormats');
                const outputFormatsContainer = document.getElementById('outputFormats');
                
                inputFormatsContainer.innerHTML = formats.input.map(format => 
                    `<span class="format-tag">${format}</span>`
                ).join('');
                
                outputFormatsContainer.innerHTML = formats.output.map(format => 
                    `<span class="format-tag">${format}</span>`
                ).join('');
                
            } catch (error) {
                console.error('Failed to load supported formats:', error);
                document.getElementById('inputFormats').innerHTML = '<span class="format-tag">Error loading</span>';
                document.getElementById('outputFormats').innerHTML = '<span class="format-tag">Error loading</span>';
            }
        }
        
        // Handle form submission
        document.getElementById('conversionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const outputFormat = document.getElementById('outputFormat').value;
            const tocOption = document.getElementById('tocOption').checked;
            const standaloneOption = document.getElementById('standaloneOption').checked;
            
            const file = fileInput.files[0];
            if (!file || !outputFormat) {
                showStatus('Please select a file and output format.', 'error');
                return;
            }
            
            const convertButton = document.getElementById('convertButton');
            convertButton.disabled = true;
            convertButton.textContent = 'Converting...';
            
            showProgress(true);
            showStatus('Starting conversion...', 'info');
            
            try {
                const options = {};
                if (tocOption) options.toc = true;
                if (standaloneOption) options.standalone = true;
                
                // Upload file
                updateProgress(10, 'Uploading file...');
                const { fileKey } = await client.uploadFile(file);
                
                // Start conversion
                updateProgress(30, 'Starting conversion...');
                const conversionResult = await client.convertDocument(fileKey, outputFormat, options);
                
                // Poll for completion
                updateProgress(50, 'Converting document...');
                const finalStatus = await client.waitForConversion(fileKey, conversionResult.jobId);
                
                // Download result
                updateProgress(90, 'Preparing download...');
                const downloadResult = await client.downloadConvertedFile(fileKey, conversionResult.jobId);
                
                updateProgress(100, 'Conversion completed!');
                
                showStatus('Conversion completed successfully!', 'success');
                showDownloadLink(downloadResult.downloadUrl, downloadResult.filename);
                
            } catch (error) {
                console.error('Conversion failed:', error);
                showStatus(`Conversion failed: ${error.message}`, 'error');
            } finally {
                convertButton.disabled = false;
                convertButton.textContent = 'Convert Document';
                setTimeout(() => showProgress(false), 2000);
            }
        });
        
        function showProgress(show) {
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = show ? 'block' : 'none';
            if (!show) {
                updateProgress(0, '');
            }
        }
        
        function updateProgress(percent, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percent}%`;
            progressText.textContent = `${percent}% - ${text}`;
        }
        
        function showStatus(message, type) {
            const statusContainer = document.getElementById('statusContainer');
            const statusText = document.getElementById('statusText');
            
            statusContainer.className = `status ${type}`;
            statusContainer.style.display = 'block';
            statusText.textContent = message;
        }
        
        function showDownloadLink(url, filename) {
            const downloadContainer = document.getElementById('downloadContainer');
            downloadContainer.innerHTML = `
                <a href="${url}" class="download-link" download="${filename}">
                    Download ${filename}
                </a>
            `;
        }
        
        // Load supported formats when page loads
        loadSupportedFormats();
    </script>
</body>
</html>
