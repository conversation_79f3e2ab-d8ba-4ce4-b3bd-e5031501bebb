/**
 * JavaScript client for document conversion API
 * Demonstrates how to upload and convert documents using the API
 */

class ConversionClient {
  constructor(baseUrl, apiKey = null) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.apiKey = apiKey;
  }

  /**
   * Upload a file using multipart upload
   */
  async uploadFile(file, key = null) {
    const fileKey = key || file.name;
    
    try {
      // Step 1: Create multipart upload
      const createResponse = await this.request(`/${fileKey}`, {
        method: 'POST'
      });
      
      if (!createResponse.ok) {
        throw new Error(`Failed to create upload: ${createResponse.statusText}`);
      }
      
      const { uploadId } = await createResponse.json();
      
      // Step 2: Upload file in chunks (simplified - single part for demo)
      const uploadResponse = await this.request(`/${fileKey}/${uploadId}/1`, {
        method: 'PUT',
        body: file
      });
      
      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload part: ${uploadResponse.statusText}`);
      }
      
      const { etag } = await uploadResponse.json();
      
      // Step 3: Complete multipart upload
      const completeResponse = await this.request(`/${fileKey}/${uploadId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parts: [{ partNumber: 1, etag }]
        })
      });
      
      if (!completeResponse.ok) {
        throw new Error(`Failed to complete upload: ${completeResponse.statusText}`);
      }
      
      return { fileKey, uploadId };
      
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  /**
   * Start document conversion
   */
  async convertDocument(fileKey, outputFormat, options = {}) {
    try {
      const response = await this.request(`/convert/${fileKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileKey,
          outputFormat,
          options
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Conversion failed: ${errorText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Conversion failed:', error);
      throw error;
    }
  }

  /**
   * Check conversion status
   */
  async getConversionStatus(fileKey, jobId) {
    try {
      const response = await this.request(`/convert/${fileKey}/status?jobId=${jobId}`, {
        method: 'GET'
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Status check failed: ${errorText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Status check failed:', error);
      throw error;
    }
  }

  /**
   * Download converted file
   */
  async downloadConvertedFile(fileKey, jobId) {
    try {
      const response = await this.request(`/convert/${fileKey}/download?jobId=${jobId}`, {
        method: 'GET'
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Download failed: ${errorText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    }
  }

  /**
   * Get supported formats
   */
  async getSupportedFormats() {
    try {
      const response = await this.request('/convert', {
        method: 'GET'
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get formats: ${errorText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error('Failed to get formats:', error);
      throw error;
    }
  }

  /**
   * Poll conversion status until completion
   */
  async waitForConversion(fileKey, jobId, maxWaitTime = 300000, pollInterval = 5000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getConversionStatus(fileKey, jobId);
      
      if (status.status === 'completed') {
        return status;
      }
      
      if (status.status === 'failed') {
        throw new Error(`Conversion failed: ${status.error || 'Unknown error'}`);
      }
      
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
    
    throw new Error('Conversion timeout');
  }

  /**
   * Complete workflow: upload and convert
   */
  async uploadAndConvert(file, outputFormat, options = {}, key = null) {
    try {
      // Upload file
      console.log('Uploading file...');
      const { fileKey } = await this.uploadFile(file, key);
      
      // Start conversion
      console.log('Starting conversion...');
      const conversionResult = await this.convertDocument(fileKey, outputFormat, options);
      
      // Wait for completion
      console.log('Waiting for conversion to complete...');
      const finalStatus = await this.waitForConversion(fileKey, conversionResult.jobId);
      
      // Download result
      console.log('Downloading converted file...');
      const downloadResult = await this.downloadConvertedFile(fileKey, conversionResult.jobId);
      
      return {
        originalFile: fileKey,
        convertedFile: downloadResult.convertedFile,
        downloadUrl: downloadResult.downloadUrl,
        status: finalStatus
      };
      
    } catch (error) {
      console.error('Upload and convert workflow failed:', error);
      throw error;
    }
  }

  /**
   * Make HTTP request with authentication
   */
  async request(path, options = {}) {
    const url = `${this.baseUrl}${path}`;
    const headers = {
      ...options.headers
    };
    
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    return fetch(url, {
      ...options,
      headers
    });
  }
}

// Example usage
async function example() {
  const client = new ConversionClient('https://your-domain.com/api/upload');
  
  try {
    // Get supported formats
    const formats = await client.getSupportedFormats();
    console.log('Supported formats:', formats);
    
    // Example file upload and conversion
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (file) {
      const result = await client.uploadAndConvert(file, 'pdf', {
        toc: true,
        standalone: true
      });
      
      console.log('Conversion completed:', result);
      
      // Create download link
      const downloadLink = document.createElement('a');
      downloadLink.href = result.downloadUrl;
      downloadLink.download = result.convertedFile;
      downloadLink.textContent = 'Download converted file';
      document.body.appendChild(downloadLink);
    }
    
  } catch (error) {
    console.error('Example failed:', error);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConversionClient;
}

// Make available globally in browser
if (typeof window !== 'undefined') {
  window.ConversionClient = ConversionClient;
}
