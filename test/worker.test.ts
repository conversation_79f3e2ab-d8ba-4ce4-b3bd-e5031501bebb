import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import app from '../src/index';

// Mock environment for testing
const mockEnv = {
  BUCKET: {
    createMultipartUpload: async (key: string) => ({
      key,
      uploadId: 'test-upload-id-123'
    }),
    resumeMultipartUpload: (key: string, uploadId: string) => ({
      uploadPart: async (partNumber: number, body: any) => ({
        partNumber,
        etag: `"etag-${partNumber}"`
      }),
      complete: async (parts: any[]) => ({
        key,
        httpEtag: '"completed-etag"'
      }),
      abort: async () => {}
    })
  }
};

// Mock environment with base path for testing
const mockEnvWithBasePath = {
  ...mockEnv,
  BASE_PATH: '/api/upload'
};

// Mock environment with conversion service for testing
const mockEnvWithConversion = {
  ...mockEnv,
  BASE_PATH: '/api/upload',
  CONVERSION_SERVICE_URL: 'http://localhost:3000',
  CONVERSION_SERVICE_TOKEN: 'test-token'
};

// Mock fetch for conversion service
const originalFetch = global.fetch;
const mockConversionService = {
  '/convert': {
    method: 'POST',
    response: {
      jobId: 'test-job-123',
      status: 'queued',
      message: 'Conversion job started successfully'
    }
  },
  '/status/test-job-123': {
    method: 'GET',
    response: {
      jobId: 'test-job-123',
      status: 'completed',
      progress: 100,
      result: {
        outputFile: '/app/output/test-job-123-document.pdf',
        filename: 'document.pdf',
        size: 1024
      }
    }
  },
  '/download/test-job-123': {
    method: 'GET',
    response: new ArrayBuffer(1024)
  },
  '/formats': {
    method: 'GET',
    response: {
      input: ['docx', 'doc', 'pdf', 'txt', 'md', 'html', 'rtf', 'odt'],
      output: ['pdf', 'docx', 'html', 'md', 'txt', 'epub', 'odt', 'rtf']
    }
  }
};

// Enhanced mock environment with R2 operations for conversion tests
const mockEnvWithR2 = {
  ...mockEnvWithConversion,
  BUCKET: {
    ...mockEnv.BUCKET,
    head: async (key: string) => ({
      key,
      size: 1024,
      httpMetadata: {
        contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    }),
    get: async (key: string) => ({
      arrayBuffer: async () => new ArrayBuffer(1024)
    }),
    put: async (key: string, body: any, options?: any) => ({
      key,
      etag: '"put-etag"'
    })
  }
};

describe('Multipart Upload Worker', () => {
  it('should create multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt', {
      method: 'POST'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    const data = await res.json() as any;
    expect(data.key).toBe('test-file.txt');
    expect(data.uploadId).toBe('test-upload-id-123');
  });

  it('should upload a part', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/1', {
      method: 'PUT',
      body: 'test data'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    const data = await res.json() as any;
    expect(data.partNumber).toBe(1);
    expect(data.etag).toBe('"etag-1"');
  });

  it('should complete multipart upload', async () => {
    const parts = [
      { partNumber: 1, etag: '"etag-1"' },
      { partNumber: 2, etag: '"etag-2"' }
    ];

    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ parts })
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    expect(res.headers.get('etag')).toBe('"completed-etag"');
  });

  it('should abort multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'DELETE'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(204);
  });

  it('should return 400 for invalid part number', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/invalid', {
      method: 'PUT',
      body: 'test data'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Invalid part number');
  });

  it('should return 400 for missing request body in upload part', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/1', {
      method: 'PUT'
      // No body
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Missing request body');
  });

  it('should return 400 for missing body in complete request', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'POST'
      // No body
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Missing or incomplete body');
  });

  it('should return 404 for unknown endpoint', async () => {
    const req = new Request('http://localhost/unknown-endpoint', {
      method: 'GET'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(404);
    const text = await res.text();
    expect(text).toBe('Not Found');
  });

  it('should return 404 for invalid path structure', async () => {
    const req = new Request('http://localhost/test-file.txt/too/many/path/segments', {
      method: 'POST'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(404);
    const text = await res.text();
    expect(text).toBe('Not Found');
  });

  describe('Base Path Support', () => {
    it('should handle requests with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt', {
        method: 'POST'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.key).toBe('test-file.txt');
      expect(data.uploadId).toBe('test-upload-id-123');
    });

    it('should return 404 for requests without base path when BASE_PATH is configured', async () => {
      const req = new Request('http://localhost/test-file.txt', {
        method: 'POST'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(404);
      const text = await res.text();
      expect(text).toBe('Not Found');
    });

    it('should handle upload part with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id/1', {
        method: 'PUT',
        body: 'test data'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.partNumber).toBe(1);
      expect(data.etag).toBe('"etag-1"');
    });

    it('should handle complete upload with base path', async () => {
      const parts = [
        { partNumber: 1, etag: '"etag-1"' },
        { partNumber: 2, etag: '"etag-2"' }
      ];

      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ parts })
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      expect(res.headers.get('etag')).toBe('"completed-etag"');
    });

    it('should handle abort upload with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id', {
        method: 'DELETE'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(204);
    });
  });

  describe('Document Conversion', () => {
    // Mock fetch for conversion service tests
    beforeEach(() => {
      global.fetch = vi.fn().mockImplementation((url: string, options?: any) => {
        const urlPath = new URL(url).pathname;

        if (urlPath === '/convert') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              jobId: 'test-job-123',
              status: 'queued',
              message: 'Conversion job started successfully'
            })
          });
        }

        if (urlPath === '/status/test-job-123') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              jobId: 'test-job-123',
              status: 'completed',
              progress: 100,
              result: {
                outputFile: '/app/output/test-job-123-document.pdf',
                filename: 'document.pdf',
                size: 1024
              }
            })
          });
        }

        if (urlPath === '/download/test-job-123') {
          return Promise.resolve({
            ok: true,
            arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
          });
        }

        if (urlPath === '/formats') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              input: ['docx', 'doc', 'pdf', 'txt', 'md', 'html', 'rtf', 'odt'],
              output: ['pdf', 'docx', 'html', 'md', 'txt', 'epub', 'odt', 'rtf']
            })
          });
        }

        return Promise.resolve({
          ok: false,
          status: 404,
          text: () => Promise.resolve('Not Found')
        });
      });
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should return 503 when conversion service is not configured', async () => {
      const req = new Request('http://localhost/api/upload/convert/document.docx', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileKey: 'document.docx',
          outputFormat: 'pdf'
        })
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(503);
      const text = await res.text();
      expect(text).toBe('Conversion service not configured');
    });

    it('should start document conversion', async () => {
      const req = new Request('http://localhost/api/upload/convert/document.docx', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileKey: 'document.docx',
          outputFormat: 'pdf',
          options: {
            toc: true,
            standalone: true
          }
        })
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.jobId).toBe('test-job-123');
      expect(data.status).toBe('queued');
      expect(data.originalFile).toBe('document.docx');
      expect(data.outputFormat).toBe('pdf');
    });

    it('should get conversion status', async () => {
      const req = new Request('http://localhost/api/upload/convert/document.docx/status?jobId=test-job-123', {
        method: 'GET'
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.jobId).toBe('test-job-123');
      expect(data.status).toBe('completed');
      expect(data.progress).toBe(100);
      expect(data.result.filename).toBe('document.pdf');
    });

    it('should return 400 when jobId is missing for status check', async () => {
      const req = new Request('http://localhost/api/upload/convert/document.docx/status', {
        method: 'GET'
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(400);
      const text = await res.text();
      expect(text).toBe('Missing jobId parameter');
    });

    it('should download converted file', async () => {
      const req = new Request('http://localhost/api/upload/convert/document.docx/download?jobId=test-job-123', {
        method: 'GET'
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.originalFile).toBe('document.docx');
      expect(data.convertedFile).toBe('document.pdf');
      expect(data.filename).toBe('document.pdf');
      expect(data.downloadUrl).toBe('/download/document.pdf');
    });

    it('should get supported formats', async () => {
      const req = new Request('http://localhost/api/upload/convert', {
        method: 'GET'
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.input).toContain('docx');
      expect(data.output).toContain('pdf');
    });

    it('should handle conversion service errors', async () => {
      // Mock a failing conversion service
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve('Internal Server Error')
      });

      const req = new Request('http://localhost/api/upload/convert/document.docx', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileKey: 'document.docx',
          outputFormat: 'pdf'
        })
      }) as any;

      const res = await app.fetch(req, mockEnvWithR2 as any);

      expect(res.status).toBe(500);
      const text = await res.text();
      expect(text).toContain('Conversion service error');
    });
  });
});
