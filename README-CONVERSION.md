# Document Conversion with Pandoc Docker

This document explains how to set up and use the pandoc document conversion feature with the API upload service.

## Quick Start

```bash
# 1. Deploy everything with one command
npm run deploy:full

# 2. Or deploy step by step
npm run docker:up          # Start conversion service
npm run deploy             # Deploy Cloudflare Worker

# 3. Test the setup
npm test

# 4. View example in browser
npm run example
```

## Overview

The conversion system consists of:
1. **Cloudflare Worker**: Handles file uploads and conversion requests
2. **Pandoc Docker Service**: Processes document conversions using pandoc
3. **Redis Queue**: Manages conversion jobs (optional but recommended)

## Architecture

```
Client → Cloudflare Worker → Pandoc Docker Service → Converted File
                ↓                      ↓
            R2 Storage ←────────── Redis Queue
```

## Setup Instructions

### 1. Start the Conversion Service

```bash
# Build and start the pandoc conversion service
docker-compose up -d

# Check if services are running
docker-compose ps

# View logs
docker-compose logs -f pandoc-converter
```

### 2. Configure Environment Variables

Update your `wrangler.toml` or set environment variables:

```toml
[vars]
BASE_PATH = "/api/upload"
CONVERSION_SERVICE_URL = "http://your-conversion-service:3000"
CONVERSION_SERVICE_TOKEN = "your-secret-token"  # Optional
```

For local development:
```bash
export CONVERSION_SERVICE_URL="http://localhost:3000"
export CONVERSION_SERVICE_TOKEN="your-secret-token"
```

### 3. Deploy the Worker

```bash
# Deploy to Cloudflare
wrangler deploy

# Or run locally for development
wrangler dev
```

## API Endpoints

### Upload and Convert Workflow

1. **Upload a file** (existing functionality):
   ```bash
   # Create multipart upload
   curl -X POST "https://your-domain.com/api/upload/document.docx"
   
   # Upload parts
   curl -X PUT "https://your-domain.com/api/upload/document.docx/upload-id/1" \
        --data-binary @part1.bin
   
   # Complete upload
   curl -X POST "https://your-domain.com/api/upload/document.docx/upload-id" \
        -H "Content-Type: application/json" \
        -d '{"parts":[{"partNumber":1,"etag":"etag1"}]}'
   ```

2. **Start conversion**:
   ```bash
   curl -X POST "https://your-domain.com/api/upload/convert/document.docx" \
        -H "Content-Type: application/json" \
        -d '{
          "fileKey": "document.docx",
          "outputFormat": "pdf",
          "options": {
            "toc": true,
            "standalone": true
          }
        }'
   ```

3. **Check conversion status**:
   ```bash
   curl "https://your-domain.com/api/upload/convert/document.docx/status?jobId=job-uuid"
   ```

4. **Download converted file**:
   ```bash
   curl "https://your-domain.com/api/upload/convert/document.docx/download?jobId=job-uuid"
   ```

### Additional Endpoints

- **Get supported formats**:
  ```bash
  curl "https://your-domain.com/api/upload/convert"
  ```

## Supported Formats

### Input Formats
- Microsoft Word (.docx, .doc)
- PDF (.pdf)
- Plain Text (.txt)
- Markdown (.md)
- HTML (.html)
- Rich Text Format (.rtf)
- OpenDocument Text (.odt)

### Output Formats
- PDF (.pdf)
- Microsoft Word (.docx)
- HTML (.html)
- Markdown (.md)
- Plain Text (.txt)
- EPUB (.epub)
- OpenDocument Text (.odt)
- Rich Text Format (.rtf)

## Conversion Options

```json
{
  "outputFormat": "pdf",
  "options": {
    "toc": true,              // Generate table of contents
    "template": "template.tex", // Use custom template
    "pdfEngine": "xelatex",   // PDF engine (xelatex, pdflatex, etc.)
    "standalone": true,       // Create standalone document
    "customArgs": ["--verbose"] // Additional pandoc arguments
  }
}
```

## Docker Service Configuration

### Environment Variables

- `MAX_FILE_SIZE`: Maximum file size (default: 100MB)
- `CONVERSION_TIMEOUT`: Conversion timeout in ms (default: 300000)
- `REDIS_URL`: Redis connection URL (default: redis://redis:6379)
- `NODE_ENV`: Environment (production/development)

### Volumes

- `pandoc_temp`: Temporary files during conversion
- `pandoc_input`: Input files storage
- `pandoc_output`: Converted files storage
- `redis_data`: Redis persistence

## Monitoring and Troubleshooting

### Health Checks

```bash
# Check conversion service health
curl http://localhost:3000/health

# Check queue status
curl http://localhost:3000/health | jq '.queue'
```

### Logs

```bash
# View conversion service logs
docker-compose logs -f pandoc-converter

# View Redis logs
docker-compose logs -f redis
```

### Common Issues

1. **Service not responding**: Check if Docker containers are running
2. **Conversion timeout**: Increase `CONVERSION_TIMEOUT` for large files
3. **Out of memory**: Increase Docker memory limits
4. **File format not supported**: Check supported formats endpoint

## Security Considerations

1. **Authentication**: Use `CONVERSION_SERVICE_TOKEN` for API authentication
2. **File validation**: Service validates file types and sizes
3. **Rate limiting**: Built-in rate limiting (100 requests per 15 minutes)
4. **Network isolation**: Use Docker networks for service communication

## Performance Tuning

1. **Scaling**: Run multiple conversion service instances
2. **Queue management**: Monitor Redis queue for bottlenecks
3. **File cleanup**: Automatic cleanup of temporary files
4. **Caching**: Consider caching frequently converted documents

## Development

### Local Development

```bash
# Start services for development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Run tests
cd conversion-service
npm test

# Debug mode
NODE_ENV=development npm run dev
```

### Adding New Formats

1. Update `supportedFormats` in `conversion-service/server.js`
2. Add format-specific pandoc options in `buildPandocCommand`
3. Update content type mapping in `getContentType`
4. Test with sample files

## Production Deployment

1. **Use production Redis**: Configure persistent Redis instance
2. **Load balancing**: Deploy multiple conversion service instances
3. **Monitoring**: Set up monitoring for queue depth and conversion times
4. **Backup**: Regular backup of Redis data and conversion templates
5. **SSL/TLS**: Use HTTPS for all API communications

## Examples

See the `examples/` directory for:
- Client-side JavaScript integration
- Python conversion client
- Batch conversion scripts
- Custom template examples
