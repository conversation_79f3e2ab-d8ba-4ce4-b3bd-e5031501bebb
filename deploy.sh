#!/bin/bash

# Deployment script for API Upload with Pandoc Conversion
# This script sets up the complete conversion infrastructure

set -e

echo "🚀 Deploying API Upload with Pandoc Conversion"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required commands are available
check_dependencies() {
    print_status "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi
    
    if ! command -v wrangler &> /dev/null; then
        missing_deps+=("wrangler")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Build and start conversion service
deploy_conversion_service() {
    print_status "Building and starting conversion service..."
    
    # Build the Docker image
    docker-compose build pandoc-converter
    
    # Start the services
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Conversion service is running"
    else
        print_error "Failed to start conversion service"
        docker-compose logs
        exit 1
    fi
    
    # Test the health endpoint
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        print_success "Conversion service health check passed"
    else
        print_warning "Conversion service health check failed, but service might still be starting"
    fi
}

# Deploy Cloudflare Worker
deploy_worker() {
    print_status "Deploying Cloudflare Worker..."
    
    # Install dependencies
    npm install
    
    # Run type check
    npm run type-check
    
    # Run tests
    print_status "Running tests..."
    npm test
    
    # Deploy to Cloudflare
    wrangler deploy
    
    print_success "Cloudflare Worker deployed successfully"
}

# Update environment configuration
update_config() {
    print_status "Updating configuration..."
    
    # Check if wrangler.toml has conversion service URL
    if grep -q "CONVERSION_SERVICE_URL" wrangler.toml; then
        print_success "Conversion service URL is configured"
    else
        print_warning "Please update CONVERSION_SERVICE_URL in wrangler.toml"
    fi
    
    # Remind about environment variables
    print_warning "Don't forget to set these environment variables if needed:"
    echo "  - CONVERSION_SERVICE_URL (for production deployment)"
    echo "  - CONVERSION_SERVICE_TOKEN (for authentication)"
}

# Run deployment tests
run_deployment_tests() {
    print_status "Running deployment tests..."
    
    # Test conversion service endpoints
    local base_url="http://localhost:3000"
    
    # Test health endpoint
    if curl -f "$base_url/health" > /dev/null 2>&1; then
        print_success "Health endpoint test passed"
    else
        print_error "Health endpoint test failed"
        return 1
    fi
    
    # Test formats endpoint
    if curl -f "$base_url/formats" > /dev/null 2>&1; then
        print_success "Formats endpoint test passed"
    else
        print_error "Formats endpoint test failed"
        return 1
    fi
    
    print_success "All deployment tests passed"
}

# Show deployment summary
show_summary() {
    echo ""
    echo "🎉 Deployment Summary"
    echo "===================="
    echo ""
    echo "✅ Conversion Service: http://localhost:3000"
    echo "✅ Cloudflare Worker: Deployed"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Update CONVERSION_SERVICE_URL in wrangler.toml for production"
    echo "2. Set up authentication token if needed"
    echo "3. Test the complete workflow with example files"
    echo "4. Monitor logs: docker-compose logs -f"
    echo ""
    echo "📚 Documentation:"
    echo "- README-CONVERSION.md - Complete setup guide"
    echo "- examples/ - Client examples and usage"
    echo ""
    echo "🔧 Useful Commands:"
    echo "- docker-compose logs -f pandoc-converter  # View conversion service logs"
    echo "- docker-compose restart                   # Restart services"
    echo "- wrangler tail                           # View worker logs"
    echo "- npm test                                # Run tests"
}

# Main deployment flow
main() {
    echo ""
    
    # Parse command line arguments
    local skip_deps=false
    local skip_service=false
    local skip_worker=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --skip-service)
                skip_service=true
                shift
                ;;
            --skip-worker)
                skip_worker=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --skip-deps     Skip dependency check"
                echo "  --skip-service  Skip conversion service deployment"
                echo "  --skip-worker   Skip Cloudflare Worker deployment"
                echo "  --help, -h      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    if [ "$skip_deps" = false ]; then
        check_dependencies
    fi
    
    if [ "$skip_service" = false ]; then
        deploy_conversion_service
    fi
    
    if [ "$skip_worker" = false ]; then
        deploy_worker
    fi
    
    update_config
    
    if [ "$skip_service" = false ]; then
        run_deployment_tests
    fi
    
    show_summary
    
    print_success "Deployment completed successfully! 🎉"
}

# Run main function with all arguments
main "$@"
