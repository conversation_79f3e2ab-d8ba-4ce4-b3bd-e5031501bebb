version: '3.8'

services:
  pandoc-converter:
    build: .
    container_name: pandoc-converter
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MAX_FILE_SIZE=100MB
      - CONVERSION_TIMEOUT=300000
      - TEMP_DIR=/app/temp
      - INPUT_DIR=/app/input
      - OUTPUT_DIR=/app/output
    volumes:
      - ./conversion-service:/app
      - pandoc_temp:/app/temp
      - pandoc_input:/app/input
      - pandoc_output:/app/output
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pandoc-network

  # Optional: Redis for job queue management
  redis:
    image: redis:7-alpine
    container_name: pandoc-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - pandoc-network

volumes:
  pandoc_temp:
  pandoc_input:
  pandoc_output:
  redis_data:

networks:
  pandoc-network:
    driver: bridge
