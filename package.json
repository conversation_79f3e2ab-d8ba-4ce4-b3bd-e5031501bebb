{"name": "api-upload", "version": "1.0.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "example": "npx http-server examples -p 8080 -o browser-example.html", "deploy:full": "./deploy.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f pandoc-converter"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250803.0", "@types/node": "^20.10.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "vitest-environment-miniflare": "^2.14.4", "wrangler": "^3.78.12"}, "dependencies": {"hono": "^4.8.12"}}