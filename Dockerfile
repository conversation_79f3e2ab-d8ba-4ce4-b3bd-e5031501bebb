# Use official pandoc image as base
FROM pandoc/latex:3.1.11

# Install additional dependencies for better document conversion
RUN apk add --no-cache \
    curl \
    nodejs \
    npm \
    python3 \
    py3-pip \
    imagemagick \
    ghostscript \
    librsvg \
    ttf-liberation \
    && rm -rf /var/cache/apk/*

# Install additional pandoc filters and tools
RUN pip3 install --no-cache-dir \
    pandoc-fignos \
    pandoc-eqnos \
    pandoc-tablenos \
    pandoc-secnos \
    pandoc-crossref

# Create app directory
WORKDIR /app

# Copy conversion service files
COPY conversion-service/ ./

# Install Node.js dependencies for the conversion service
RUN npm install

# Create directories for file processing
RUN mkdir -p /app/input /app/output /app/temp

# Set proper permissions
RUN chmod +x /app/convert.sh

# Expose port for the conversion service
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the conversion service
CMD ["npm", "start"]
